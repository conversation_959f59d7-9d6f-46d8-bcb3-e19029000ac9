<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\JobVacancy;
use App\Models\JobCategory;
use App\Models\JobApplication;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class JobApplicationSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $vipUser;
    protected $admin;
    protected $job;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Criar usuários de teste
        $this->user = User::factory()->create([
            'role' => 'visitante',
            'name' => '<PERSON> Silva',
            'email' => '<EMAIL>',
            'telefone' => '11999999999',
        ]);

        $this->vipUser = User::factory()->create([
            'role' => 'vip',
            'name' => 'Maria VIP',
            'email' => '<EMAIL>',
        ]);

        $this->admin = User::factory()->create([
            'role' => 'administrador',
            'name' => 'Admin',
            'email' => '<EMAIL>',
        ]);

        // Criar categoria e vaga de teste
        $this->category = JobCategory::factory()->create([
            'name' => 'Tecnologia',
            'slug' => 'tecnologia',
        ]);

        $this->job = JobVacancy::factory()->create([
            'title' => 'Desenvolvedor PHP',
            'slug' => 'desenvolvedor-php',
            'category_id' => $this->category->id,
            'requires_resume' => true,
            'requires_cover_letter' => true,
            'is_active' => true,
        ]);

        // Configurar storage fake
        Storage::fake('private');
        
        // Fake notifications
        Notification::fake();
    }

    /** @test */
    public function user_can_submit_job_application_with_all_required_fields()
    {
        $this->actingAs($this->user);

        $file = UploadedFile::fake()->create('curriculo.pdf', 1000, 'application/pdf');

        $response = $this->post(route('job_vacancies.apply', $this->job->id), [
            'name' => 'João Silva',
            'email' => '<EMAIL>',
            'phone' => '11999999999',
            'experience' => 'Experiência em desenvolvimento web com PHP e Laravel.',
            'cover_letter' => 'Esta é minha carta de apresentação para a vaga de desenvolvedor PHP. Tenho experiência sólida em desenvolvimento web e estou muito interessado nesta oportunidade.',
            'resume' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verificar se a candidatura foi criada
        $this->assertDatabaseHas('job_applications', [
            'job_id' => $this->job->id,
            'user_id' => $this->user->id,
            'candidate_name' => 'João Silva',
            'candidate_email' => '<EMAIL>',
            'candidate_phone' => '11999999999',
            'status' => 'Pendente',
            'priority_level' => 'normal',
        ]);

        // Verificar se o arquivo foi salvo
        $application = JobApplication::where('user_id', $this->user->id)->first();
        $this->assertNotNull($application->resume_path);
        Storage::disk('private')->assertExists($application->resume_path);
    }

    /** @test */
    public function vip_user_gets_high_priority_application()
    {
        $this->actingAs($this->vipUser);

        $file = UploadedFile::fake()->create('curriculo.pdf', 1000, 'application/pdf');

        $this->post(route('job_vacancies.apply', $this->job->id), [
            'name' => 'Maria VIP',
            'email' => '<EMAIL>',
            'phone' => '11888888888',
            'experience' => 'Experiência VIP',
            'cover_letter' => 'Carta de apresentação VIP com mais de 100 caracteres para atender aos requisitos mínimos da validação.',
            'resume' => $file,
        ]);

        $this->assertDatabaseHas('job_applications', [
            'user_id' => $this->vipUser->id,
            'is_vip_priority' => true,
            'priority_level' => 'alta',
        ]);
    }

    /** @test */
    public function application_stores_audit_information()
    {
        $this->actingAs($this->user);

        $file = UploadedFile::fake()->create('curriculo.pdf', 1000, 'application/pdf');

        $this->post(route('job_vacancies.apply', $this->job->id), [
            'name' => 'João Silva',
            'email' => '<EMAIL>',
            'phone' => '11999999999',
            'experience' => 'Experiência teste',
            'cover_letter' => 'Carta de apresentação com mais de 100 caracteres para atender aos requisitos mínimos da validação do sistema.',
            'resume' => $file,
        ]);

        $application = JobApplication::where('user_id', $this->user->id)->first();

        // Verificar dados de auditoria
        $this->assertNotNull($application->ip_address);
        $this->assertNotNull($application->user_agent);
        $this->assertNotNull($application->form_data);
        
        // Verificar estrutura dos dados do formulário
        $formData = $application->form_data;
        $this->assertArrayHasKey('name', $formData);
        $this->assertArrayHasKey('email', $formData);
        $this->assertArrayHasKey('phone', $formData);
        $this->assertArrayHasKey('submitted_at', $formData);
        $this->assertEquals('João Silva', $formData['name']);
    }

    /** @test */
    public function user_cannot_apply_twice_for_same_job()
    {
        $this->actingAs($this->user);

        // Primeira candidatura
        JobApplication::create([
            'job_id' => $this->job->id,
            'job_vacancy_id' => $this->job->id,
            'user_id' => $this->user->id,
            'status' => 'Pendente',
            'candidate_name' => 'João Silva',
            'candidate_email' => '<EMAIL>',
        ]);

        $file = UploadedFile::fake()->create('curriculo.pdf', 1000, 'application/pdf');

        // Tentar segunda candidatura
        $response = $this->post(route('job_vacancies.apply', $this->job->id), [
            'name' => 'João Silva',
            'email' => '<EMAIL>',
            'phone' => '11999999999',
            'cover_letter' => 'Segunda tentativa de candidatura',
            'resume' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Você já se candidatou para esta vaga.');
    }

    /** @test */
    public function application_validates_required_fields()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('job_vacancies.apply', $this->job->id), [
            // Campos obrigatórios em branco
        ]);

        $response->assertSessionHasErrors([
            'name',
            'email',
            'phone',
            'resume',
            'cover_letter',
        ]);
    }

    /** @test */
    public function application_validates_file_types()
    {
        $this->actingAs($this->user);

        $invalidFile = UploadedFile::fake()->create('curriculo.txt', 1000, 'text/plain');

        $response = $this->post(route('job_vacancies.apply', $this->job->id), [
            'name' => 'João Silva',
            'email' => '<EMAIL>',
            'phone' => '11999999999',
            'cover_letter' => 'Carta de apresentação válida com mais de 100 caracteres para atender aos requisitos.',
            'resume' => $invalidFile,
        ]);

        $response->assertSessionHasErrors(['resume']);
    }

    /** @test */
    public function notifications_are_sent_on_successful_application()
    {
        $this->actingAs($this->user);

        $file = UploadedFile::fake()->create('curriculo.pdf', 1000, 'application/pdf');

        $this->post(route('job_vacancies.apply', $this->job->id), [
            'name' => 'João Silva',
            'email' => '<EMAIL>',
            'phone' => '11999999999',
            'experience' => 'Experiência teste',
            'cover_letter' => 'Carta de apresentação com mais de 100 caracteres para atender aos requisitos mínimos.',
            'resume' => $file,
        ]);

        // Verificar se notificações foram enviadas
        Notification::assertSentTo($this->admin, \App\Notifications\JobApplicationSubmitted::class);
        Notification::assertSentTo($this->user, \App\Notifications\JobApplicationConfirmation::class);
    }

    /** @test */
    public function job_application_counter_is_incremented()
    {
        $initialCount = $this->job->applications_count;
        
        $this->actingAs($this->user);

        $file = UploadedFile::fake()->create('curriculo.pdf', 1000, 'application/pdf');

        $this->post(route('job_vacancies.apply', $this->job->id), [
            'name' => 'João Silva',
            'email' => '<EMAIL>',
            'phone' => '11999999999',
            'cover_letter' => 'Carta de apresentação válida com mais de 100 caracteres.',
            'resume' => $file,
        ]);

        $this->job->refresh();
        $this->assertEquals($initialCount + 1, $this->job->applications_count);
    }
}
